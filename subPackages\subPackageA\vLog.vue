<template>
	<view>
		<view class="h_20rpx"></view>

		<!-- 版本列表 -->
		<view class="version-container">
			<block v-for="(item, index) in versionList" :key="item.id">
				<view class="list-public version-item">
					<!-- 版本号和类型标签 -->
					<view class="display-a-js margin-bottom_20rpx">
						<view class="version-number">{{item.version}}</view>
						<view class="version-type-tag"
							:class="{'version-type-major': item.type === 'major', 'version-type-minor': item.type === 'minor', 'version-type-patch': item.type === 'patch'}">
							{{getVersionTypeText(item.type)}}
						</view>
					</view>

					<!-- 更新内容 -->
					<view class="version-content margin-bottom_20rpx">
						{{item.content}}
					</view>

					<!-- 更新日期 -->
					<view class="version-date">
						{{item.updateDate}}
					</view>
				</view>
			</block>
		</view>
		<view style="height: 160rpx;"></view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 版本更新记录列表
				versionList: []
			}
		},

		onLoad() {
			// 页面加载时获取版本更新记录
			this.getVersionList();
		},
		methods: {
			// 获取版本更新记录列表
			async getVersionList() {
				try {
					const result = await this.$http.post({
						url: this.$api.system_updateLog_list,
						data: {}
					});

					if (result.errno === 1) {
						// 数据映射和类型转换
						this.versionList = result.data.list.map(item => {
							return {
								id: item.id,
								version: item.version,
								content: item.content,
								updateDate: item.sys_update_time,
								type: this.mapApiTypeToPageType(item.type)
							};
						});
					} else {
						this.$sun.toast(result.message || '获取版本记录失败', 'none');
					}
				} catch (error) {
					console.error('获取版本记录失败:', error);
					this.$sun.toast('网络请求失败', 'none');
				}
			},

			// 将API返回的type映射为页面使用的type
			mapApiTypeToPageType(apiType) {
				switch (apiType) {
					case 1:
						return 'major';  // 重大更新
					case 2:
						return 'minor';  // 功能更新
					case 3:
						return 'patch';  // 问题修复
					default:
						return 'minor';  // 默认为功能更新
				}
			},

			// 获取版本类型对应的文本
			getVersionTypeText(type) {
				switch (type) {
					case 'major':
						return '重大更新';
					case 'minor':
						return '功能更新';
					case 'patch':
						return '问题修复';
					default:
						return '功能更新';
				}
			}
		}
	}
</script>

<style>
	.list-public {
		width: auto;
	}

	/* 页面标题样式 */
	.page-header {
		padding: 30rpx 20rpx;
		background-color: #FFFFFF;
		margin-bottom: 20rpx;
	}

	.page-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
		text-align: center;
	}

	/* 版本容器 */
	.version-container {
		padding: 0 20rpx;
	}

	/* 版本项目样式 */
	.version-item {
		margin-bottom: 20rpx;
		background-color: #2A2D42 !important;
		/* 覆盖list-public的白色背景，使用与页面背景协调的深色 */
	}

	/* 版本号样式 */
	.version-number {
		font-size: 32rpx;
		font-weight: bold;
		color: #5DADE2;
		/* 调整为更亮的蓝色以适应深色背景 */
	}

	/* 版本类型标签基础样式 */
	.version-type-tag {
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		font-size: 22rpx;
		color: #FFFFFF;
		font-weight: bold;
	}

	/* 重大更新标签 */
	.version-type-major {
		background-color: #28B463;
		/* 调整绿色饱和度 */
	}

	/* 功能更新标签 */
	.version-type-minor {
		background-color: #3498DB;
		/* 调整蓝色以适应深色背景 */
	}

	/* 问题修复标签 */
	.version-type-patch {
		background-color: #F39C12;
		/* 调整橙色以适应深色背景 */
	}

	/* 更新内容样式 */
	.version-content {
		font-size: 28rpx;
		color: #E8E8E8;
		/* 改为浅色以适应深色背景 */
		line-height: 1.6;
	}

	/* 更新日期样式 */
	.version-date {
		font-size: 26rpx;
		color: #B0B0B0;
		/* 改为浅灰色以适应深色背景 */
		text-align: right;
	}

	page {
		width: 100%;
		overflow-x: hidden;
		border-top: none;
		background-color: #202336;
	}
</style>