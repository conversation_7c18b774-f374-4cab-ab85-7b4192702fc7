<template>
	<view>
		<view class="h_20rpx"></view>

		<!-- 版本列表 -->
		<view class="version-container">
			<block v-for="(item, index) in versionList" :key="item.id">
				<view class="list-public version-item">
					<!-- 版本号和类型标签 -->
					<view class="display-a-js margin-bottom_20rpx">
						<view class="version-number">{{item.version}}</view>
						<view class="version-type-tag">
							{{getVersionTypeText(item.type)}}
						</view>
					</view>

					<!-- 更新内容 -->
					<view class="version-content margin-bottom_20rpx">
						{{item.content}}
					</view>

					<!-- 更新日期 -->
					<view class="version-date">
						{{item.updateDate}}
					</view>
				</view>
			</block>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 版本更新记录固定数据
				versionList: [{
						id: 1,
						version: "v2.1.0",
						content: "新增AI智能剪辑功能，优化用户体验，修复已知问题，提升系统稳定性",
						updateDate: "2024-01-15",
						type: "major"
					},
					{
						id: 2,
						version: "v2.0.5",
						content: "修复视频上传失败的问题，优化网络连接稳定性",
						updateDate: "2024-01-10",
						type: "patch"
					},
					{
						id: 3,
						version: "v2.0.4",
						content: "新增数字人形象管理功能，支持自定义形象设置",
						updateDate: "2024-01-05",
						type: "minor"
					},
					{
						id: 4,
						version: "v2.0.3",
						content: "修复音频同步问题，优化渲染性能",
						updateDate: "2023-12-28",
						type: "patch"
					},
					{
						id: 5,
						version: "v2.0.2",
						content: "新增批量处理功能，支持多文件同时上传和处理",
						updateDate: "2023-12-20",
						type: "minor"
					},
					{
						id: 6,
						version: "v2.0.1",
						content: "修复登录状态异常问题，优化用户体验流程",
						updateDate: "2023-12-15",
						type: "patch"
					},
					{
						id: 7,
						version: "v2.0.0",
						content: "全新版本发布！重构核心架构，新增AI数字人功能，支持实时语音合成",
						updateDate: "2023-12-01",
						type: "major"
					},
					{
						id: 8,
						version: "v1.9.8",
						content: "优化界面设计，提升用户交互体验，修复若干已知问题",
						updateDate: "2023-11-25",
						type: "minor"
					}
				]
			}
		},
		methods: {
			// 获取版本类型对应的CSS类
			getVersionTypeClass(type) {
				switch (type) {
					case 'major':
						return 'version-type-major';
					case 'minor':
						return 'version-type-minor';
					case 'patch':
						return 'version-type-patch';
					default:
						return 'version-type-minor';
				}
			},

			// 获取版本类型对应的文本
			getVersionTypeText(type) {
				switch (type) {
					case 'major':
						return '重大更新';
					case 'minor':
						return '功能更新';
					case 'patch':
						return '问题修复';
					default:
						return '功能更新';
				}
			}
		}
	}
</script>

<style>
	/* 页面标题样式 */
	.page-header {
		padding: 30rpx 20rpx;
		background-color: #FFFFFF;
		margin-bottom: 20rpx;
	}

	.page-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
		text-align: center;
	}

	/* 版本容器 */
	.version-container {
		padding: 0 20rpx;
	}

	/* 版本项目样式 */
	.version-item {
		margin-bottom: 20rpx;
	}

	/* 版本号样式 */
	.version-number {
		font-size: 32rpx;
		font-weight: bold;
		color: #49BDF6;
	}

	/* 版本类型标签基础样式 */
	.version-type-tag {
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		font-size: 22rpx;
		color: #FFFFFF;
		font-weight: bold;
	}

	/* 重大更新标签 */
	.version-type-major {
		background-color: #20FF86;
	}

	/* 功能更新标签 */
	.version-type-minor {
		background-color: #49BDF6;
	}

	/* 问题修复标签 */
	.version-type-patch {
		background-color: #FFC100;
	}

	/* 更新内容样式 */
	.version-content {
		font-size: 28rpx;
		color: #333333;
		line-height: 1.6;
	}

	/* 更新日期样式 */
	.version-date {
		font-size: 26rpx;
		color: #999999;
		text-align: right;
	}

	page {
		background-color: #111317;
		border: none;
	}
</style>