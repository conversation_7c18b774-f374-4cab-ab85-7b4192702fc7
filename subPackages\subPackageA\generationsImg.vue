<template>
	<view class="container">
		<scroll-view class="scroll-area" scroll-y="true" :scroll-top="scrollTop" :scroll-with-animation="true"
			@scrolltolower="onScrollLower" show-scrollbar="false">
			<view class="list">
				<chat-item :showTextFavorite="showTextFavorite" :showImageFavorite="showImageFavorite"
					:showVideoFavorite="showVideoFavorite" v-for="(item, index) in list" :key="index" :item="item"
					:image-urls="imageUrls" @image-load="imageLoaded" @image-error="imageError"
					:sessionType="type"></chat-item>

				<!-- 加载中提示 --> 
				<view class="loading-container" v-if="isStart">
					<view class="loading-dots">
						<view class="dot"></view>
						<view class="dot"></view>
						<view class="dot"></view>
					</view>
				</view>
			</view>
			<!-- 底部占位 - 根据实际输入框高度、横向功能区高度和键盘高度动态调整 -->
			<view class="bottom-placeholder" :style="{ height: bottomPlaceholderHeight }"></view>
		</scroll-view>



        <view class="bottom" :style="{
            bottom: 0,
            transform: isKeyboardShow ? 'translateY(0)' : 'translateY(0)',
        }" :class="{ 'keyboard-visible': isKeyboardShow }">
            <!-- 参考素材预览区 -->
            <view class="ref-preview-bar" v-show="isReferenceOpen">
                <view class="ref-preview-scroller">
                    <!-- 图片预览 -->
                    <view class="ref-preview-item" v-if="referenceImages">
                        <image :src="referenceImages" class="ref-preview-img" mode="aspectFill"></image>
                        <view class="ref-preview-del" @click.stop="deleteReferenceImage(0)">×</view>
                    </view>
                    <!-- 视频预览 -->
                    <view class="ref-preview-item" v-if="referenceVideo">
                        <video :src="referenceVideo" class="ref-preview-video" :controls="false" :show-center-play-btn="true"></video>
                        <view class="ref-preview-del" @click.stop="deleteReferenceVideo">×</view>
                    </view>
                </view>
                <view class="ref-preview-close" @click="clearReferencePreview">×</view>
            </view>

            <!-- 横向滚动功能区 -->
            <scroll-view class="tab-row" v-if="tabBar.length" show-scrollbar="false">
                <view class="tab-scroller">
                    <view class="tab-chip" :class="{'tab-chip--active': t.type === 'check' && t.selected}"
                        v-for="t in tabBar" :key="t.key" @click="handleTabClick(t)">
						<text v-if="t.icon" class="iconfont" :class="'icon-' + t.icon" :style="{ marginRight: '10rpx', color: t.type === 'check' && t.selected ? '#bfdbfe' : '#e5e7eb'}"></text>
                        <text class="tab-label">{{ t.label }}</text>
                    </view>
                </view>
            </scroll-view>

            <view class="composer">
                <!-- 左侧：选择图片 -->
                <view class="icon-btn" v-show="[17,18,19,20,21].includes(type)" @click="chooseReferenceImage">
					<text class="iconfont icon-jiahao" style="font-size: 50rpx; color: #3b82f6;"></text>
                </view>

                <!-- 中间：输入区域 -->
                <view class="input-container composer-input">
                    <textarea v-model="content" class="r-input r-input--light" placeholder="发消息..."
                        placeholder-class="placeholder-light" @confirm="send" @focus="inputFocus" @blur="inputBlur"
                        :adjust-position="true" cursor-spacing="20" auto-height :maxlength="-1"
                        :style="{ height: textareaHeight, maxHeight: textareaMaxHeight }"
                        @input="adjustTextareaHeight"></textarea>
                </view>

                <!-- 右侧：发送按钮 -->
                <view class="actions">
                    <text v-show="!(!canSend&&type==21)" class="iconfont icon-xiangshangyuanjiantoushangjiantoumianxing" :style="{ fontSize: '50rpx', color: '#3b82f6', opacity: canSend ? 1 : 0.4}"  @click="send()"></text>
                    <!-- <view v-show="(!canSend&&type==21)" @click="toggleMore">
						<text v-if="!isMoreOpen" class="iconfont icon-jiahao" style="font-size: 50rpx; color: #3b82f6;"></text>
						<text v-else class="iconfont icon-chacha" style="font-size: 50rpx; color: #3b82f6;"></text>
					</view> -->
                </view>
            </view>

			<!-- 更多操作面板--暂时隐藏 -->
			<view class="more-panel" v-if="isMoreOpen">
				<view class="more-grid">
					<view class="more-item" @click="onPickPhoto">
						<text class="iconfont icon-zhaopian_huabanfuben" style="font-size: 50rpx; color: #3b82f6;"></text>
						<text class="more-item-label">选择照片</text>
					</view>
					<view class="more-item" @click="onPickFile">
						<text class="iconfont icon-xuanzeshipinwenjian" style="font-size: 50rpx; color: #3b82f6;"></text>
						<text class="more-item-label">选择视频</text>
					</view>
				</view>
			</view>
        </view>
		<!-- 历史记录容器 - 带动画效果 -->
		<view class="history-mask" v-if="isShowHistory" @click="hideHistory"></view>
		<view class="history-container" v-if="isShowHistory" :class="{ 'history-container-active': isShowHistory }">
			<view class="history-header">
				<text class="history-title">历史记录</text>
				<view class="history-close" @click="hideHistory">×</view>
			</view>
			<view class="history-content">
				<view class="history-item" :class="{ 'history-item-active': session_id == item.dialogue_id }"
					v-for="item in historyList" :key="item.dialogue_id" @click="selectHistoryItem(item)">
					<view class="history-item-content">{{ item.first_content }}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import ChatItem from './components/chat-item/chat-item.vue';
	import {
		decodedString
	} from './utils/decodedString.js'
	// 阿里云直传所需算法工具
	const base64 = require('@/utils/ali-oos/base64.js');
	require('@/utils/ali-oos/hmac.js');
	require('@/utils/ali-oos/sha1.js');
	const Crypto = require('@/utils/ali-oos/crypto.js');
	import './iconfont/iconfont.css';

	export default {
		components: {
			ChatItem
		},
		data() {
			return {
				video_title: '', //视频标题
				video_content: '', //视频内容
                tabBar: [
					{ type: 'btn', key: 'history', label: '历史记录', icon: 'lishijilu' },
					{ type: 'btn', key: 'newchat', label: '新建会话', icon: 'xinjianhuihua' },
				],
				isShowHistory: false,
				historyList: [],
				content: '',
				list: [],
				isStart: false,
				scrollTop: 0,
				safeAreaBottom: 0,
				keyboardHeight: 0, // 键盘高度
				isKeyboardShow: false, // 键盘是否显示
				// 保存所有AI生成的图片URL列表，用于多图预览
				imageUrls: [],
				platform: '',
				focusTimer: null,
				scrollTimer: null,
				windowHeight: 0, // 窗口高度
				contentHeight: 0, // 内容高度
				inputBarHeight: 120, // 输入框区域高度，单位rpx
				tabRowHeight: 100, // 横向滚动功能区高度，单位rpx
				needScrollToBottom: true, // 是否需要滚动到底部
				isScrolling: false, // 是否正在滚动中
				lastScrollTime: 0, // 上次滚动时间
				scrollDebounceDelay: 200, // 滚动防抖延迟(ms)
				keyboardStateChangeTime: 0, // 键盘状态最后变化时间
				isAutoScrollPrevented: false, // 是否暂时阻止自动滚动
				keyboardTransitionDuration: 300, // 键盘过渡动画持续时间(ms)
				session_id: '', // 会话id
				cueWord: '', // 提示词
				type: null,
				requestTask: null,
				textareaHeight: '40rpx', // 文本域的初始高度
				textareaMinHeight: '40rpx', // 文本域的最小高度
				textareaMaxHeight: '300rpx', // 文本域的最大高度
				referenceImages: '', // 参考图片列表
                referenceVideo: '', // 参考视频URL（供预览）
				taskId: null, // 任务ID
				pollTimer: null, // 轮询定时器
                isMoreOpen: false, // 更多操作是否展开
				isReferenceOpen: false, // 参考图片是否展开
                // ===== 阿里云直传视频相关 =====
                upPicUrl2: '', // OSS 上传域名
                uploadProgress: 0, // 上传进度
                formData: {
                    key: '',
                    policy: '',
                    OSSAccessKeyId: '',
                    signature: '',
                    success_action_status: '200',
                },
                policyText: {
                    expiration: '2030-01-01T12:00:00.000Z',
                    conditions: [
                        ['content-length-range', 0, 524288000] // 最大 500MB
                    ]
                },
			}
		},
		computed: {
            canSend(){
                if([17,18,19,20].includes(this.type)){
                    return !!this.referenceImages
                }
				if(this.type === 21){
					return !!this.referenceImages || !!this.referenceVideo || this.content && this.content.trim().length>0
				}
                return this.content && this.content.trim().length>0
            },
            hasContent(){
                return this.content && this.content.trim().length>0
            },
            // 仅选中项的键值对对象（key:true）
            selectedTagMap(){
                if (!Array.isArray(this.tabBar)) return {}
                const map = {}
                this.tabBar.forEach(t => { if (t && t.selected) map[t.key] = true })
                return map
            },
			showVideoFavorite() {
				return this.type === 20
			},
			showImageFavorite() {
				return [14, 17, 18, 19].includes(this.type)
			},
			showTextFavorite() {
				return ![14, 17, 18, 19, 20].includes(this.type)
			},
			// 计算底部输入框的bottom值
			bottomPosition() {
				// 如果键盘显示，返回键盘高度，否则返回安全区域高度
				return this.isKeyboardShow ?
					this.keyboardHeight + 'px' :
					this.safeAreaBottom + 'px'
			},
			// 计算底部占位符高度
			bottomPlaceholderHeight() {
				// 计算横向滚动功能区是否显示
				const hasTabBar = this.tabBar && this.tabBar.length > 0;
				
				// 根据不同状态计算底部占位符高度
				if (this.isKeyboardShow) {
					// 键盘显示时，考虑键盘高度 + 输入框高度 + 横向功能区高度
					const rpxToPx = this.windowHeight / 750 // 计算rpx到px的转换比例
					const inputHeightPx = this.inputBarHeight * rpxToPx // 将rpx转换为px
					const tabRowHeightPx = hasTabBar ? this.tabRowHeight * rpxToPx : 0 // 将rpx转换为px

					// 键盘高度 + 输入框高度 + 横向功能区高度 + 额外间距
					return this.keyboardHeight + inputHeightPx + tabRowHeightPx + 20 + 'px'
				} else {
					// 键盘隐藏时，考虑输入框高度 + 横向功能区高度 + 安全区域
					const totalHeight = this.inputBarHeight + (hasTabBar ? this.tabRowHeight : 0) + 20;
					return totalHeight + 'rpx'
				}
			},
		},
		watch: {
			// 监听键盘状态变化，更新UI
			isKeyboardShow(newVal, oldVal) {
				if (newVal !== oldVal) {
					// 键盘状态发生变化时，强制重新计算布局
					this.$nextTick(() => {
						// 延迟执行，确保DOM已更新
						setTimeout(() => {
							this.forceLayout()
							// 确保横向功能区高度正确计算
							this.updateTabRowHeight()
							if (newVal) {
								// 键盘弹出时，给页面元素足够时间适应新布局再滚动
								setTimeout(() => {
									if (this.isKeyboardShow) {
										// 再次检查，防止状态快速变化
										this.scrollToBottomDebounced()
									}
								}, this.keyboardTransitionDuration)
							}
						}, 300)
					})
				}
			},
			// 监听内容变化，自动滚动到底部，但增加防抖
			list: {
				handler(newVal, oldVal) {
					if (this.needScrollToBottom && newVal.length > 0) {
						// 如果是添加了新消息
						if (oldVal.length > 0 && newVal.length > oldVal.length) {
							this.$nextTick(() => {
								this.scrollToBottomDebounced()
							})
						}
					}
				},
				deep: true, // 深度监听
			},
			// 监听输入内容变化，当有内容时隐藏更多操作面板
			content(newVal, oldVal) {
				if (newVal && newVal.trim().length > 0 && this.isMoreOpen) {
					// 当输入框有内容且更多操作面板打开时，隐藏面板
					this.isMoreOpen = false
				}
			},
			// 监听横向功能区变化，重新计算高度
			tabBar: {
				handler(newVal, oldVal) {
					// 当横向功能区显示或隐藏时，重新计算高度
					this.$nextTick(() => {
						this.updateTabRowHeight()
						// 重新计算布局并滚动到底部
						this.forceLayout()
						this.scrollToBottomDebounced()
					})
				},
				deep: true
			},
		},
		onLoad(options) {
			if (options.type) {
				this.type = Number(options.type)

				if (this.type === 21) {
					this.tabBar.splice(1, 0, { type: 'check', key: 'thinking', label: '深度思考', selected: true, icon: 'shendusikao' })
				}
			}
			if (options.title) {
				uni.setNavigationBarTitle({
					title: options.title
				})
			}

			// if (![9].includes(this.type)) {
			// 获取提示词
			this.getCueWord()
			// }
			// if (uni.getStorageSync('AIImgList')) {
			// 	this.list = uni.getStorageSync('AIImgList')
			// }



			console.log(this.list);
			// 获取安全区域高度
			this.getSafeAreaBottom()
			// 监听键盘高度变化
			this.listenKeyboardHeight()
			// 获取窗口高度
			this.getWindowHeight()

            // 初始化阿里云直传配置
            this.getAliyunConfig()
            
            // 初始化横向功能区高度
            this.$nextTick(() => {
                this.updateTabRowHeight()
            })

		},
		onUnload() {
			// 清理轮询定时器
			if (this.pollTimer) {
				clearTimeout(this.pollTimer);
				this.pollTimer = null;
			}
			// 页面卸载时移除键盘高度监听
			this.removeKeyboardListener()
			uni.setStorageSync('AIImgList', this.list)
		},
		onReady() {
			// 页面加载完成后，设置平台信息
			this.initPlatformInfo()


		},
		onShow() {
			// 页面显示时重新计算布局
			this.forceLayout()
			// 确保横向功能区高度正确计算
			this.$nextTick(() => {
				this.updateTabRowHeight()
			})
		},
		mounted() {
			// 初始化时监听页面尺寸变化，以便检测键盘弹出
			uni.onWindowResize((res) => {
				console.log('窗口尺寸变化', res)
				// 记录键盘状态变化时间
				this.keyboardStateChangeTime = Date.now()

				// 计算窗口高度变化，判断键盘是否弹出
				const heightChange = this.windowHeight - res.size.windowHeight
				console.log('高度变化:', heightChange)

				if (heightChange > 100) {
					// 大幅度高度减小，键盘弹出
					console.log('键盘弹出')
					this.isKeyboardShow = true
					this.keyboardHeight = heightChange
				} else if (this.isKeyboardShow && heightChange < 50) {
					// 高度恢复，键盘收起
					console.log('键盘收起')
					this.isKeyboardShow = false
					this.keyboardHeight = 0

					// 键盘收起时，短暂阻止自动滚动
					this.isAutoScrollPrevented = true
					setTimeout(() => {
						this.isAutoScrollPrevented = false
					}, this.keyboardTransitionDuration + 100)
				}
			})
		},
		methods: {
            // 关闭预览区域
            clearReferencePreview(){
                this.referenceImages = ''
                this.referenceVideo = ''
				this.isReferenceOpen = false
            },
			handleTabClick(tab){
				switch(tab.key){
					case 'history':
						this.showHistory()
						break
					case 'thinking':
						this.toggleTab(tab)
						break
					case 'newchat':
						this.handlerNewChat()
						break
				}
			},
            // 切换单个标签的选中状态
            toggleTab(tab){
                tab.selected = !tab.selected
                // 切换标签后重新计算布局
                this.$nextTick(() => {
                    this.updateTabRowHeight()
                    this.scrollToBottomDebounced()
                })
            },
			// 新建会话
			handlerNewChat(){
				if(this.list.length === 1 && this.list[0].role === 'assistant') {
					this.$sun.toast('已在新会话中', 'none');
					return;
				}
				this.list = []
				this.session_id = ''
				this.getCueWord()
				// 获取安全区域高度
				this.getSafeAreaBottom()
				// 监听键盘高度变化
				this.listenKeyboardHeight()
				// 获取窗口高度
				this.getWindowHeight()

				// 初始化阿里云直传配置
				this.getAliyunConfig()
			
				// 滚动到底部
				this.$nextTick(() => {
					this.scrollToBottomDebounced()
				})
			},
            // 展开/收起更多
            toggleMore(){
                this.isMoreOpen = !this.isMoreOpen
            },
            // 选择照片
            onPickPhoto(){
                this.chooseReferenceImage()
                this.isMoreOpen = false
            },
            // 选择视频并上传（直传阿里云）
            async onPickFile(){
                try {
                    if (!this.upPicUrl2) {
                        await this.getAliyunConfig()
                    }
                    if (!this.upPicUrl2) {
                        this.$sun.toast('请配置阿里云', 'none')
                        return
                    }
                    uni.chooseVideo({
                        count: 1,
                        compressed: false,
                        sourceType: ['album'],
                        success: (res) => {
                            let file = res.tempFilePath
                            let suffix = 'mp4'
                            if (res.tempFilePath) {
                                const parts = res.tempFilePath.split('.')
                                suffix = parts[parts.length - 1]
                            } else {
                                this.$sun.toast('视频资源异常,请重新选择!', 'none')
                                return
                            }
                            this.uploadBaseVideo(file, suffix)
                        },
                        fail: (err) => {
                            console.log('uni.chooseVideo err ---->', err)
                        }
                    })
                } catch (e) {
                    console.error(e)
                }
            },

            // 直传阿里云：上传视频
            uploadBaseVideo(file, suffix){
                let count = 0
                let timer = null
                uni.showLoading({ title: '上传中...' + count + '%', mask: true })
                timer = setInterval(() => {
                    uni.showLoading({ title: `上传中... ${count}%` })
                }, 300)

                this.formData.key = new Date().getTime() + Math.floor(Math.random() * 150) + '.' + suffix

                const task = uni.uploadFile({
                    url: this.upPicUrl2,
                    filePath: file,
                    fileType: 'video/mp4',
                    name: 'file',
                    formData: this.formData,
                    header: {},
                    success: (uploadRes) => {
                        if (uploadRes.statusCode != 200) {
                            uni.showToast({ title: '上传失败 : ' + uploadRes.data, icon: 'none' })
                            clearInterval(timer)
                            uni.hideLoading()
                            return
                        }
                        count = 100
                        const videoUrl = this.upPicUrl2 + '/' + this.formData.key
                        clearInterval(timer)
                        uni.hideLoading()

                        // 在预览条显示视频（仅预览，不自动发送消息）
                        this.referenceVideo = videoUrl
                        this.isReferenceOpen = true
                        this.isMoreOpen = false
                        // 不自动 push 消息，不触发 send/startStream
                    },
                    fail: (e) => {
                        uni.showToast({ title: '上传失败,' + e, icon: 'none' })
                        clearInterval(timer)
                        uni.hideLoading()
                    }
                })

                task.onProgressUpdate((res) => {
                    if (res.progress > 0 && count < 100) {
                        count = res.progress
                        this.uploadProgress = res.progress
                    }
                })
            },

            // 获取阿里云直传配置
            async getAliyunConfig(){
                try {
                    const result = await this.$http.post({ url: this.$api.aliyunConfig })
                    if (result.errno === 0) {
                        this.upPicUrl2 = 'https://' + result.data.alioss_domain
                        this.formData.OSSAccessKeyId = result.data.alioss_access_key_id
                        this.formData.policy = base64.encode(JSON.stringify(this.policyText))
                        const message = this.formData.policy
                        const bytes = Crypto.HMAC(Crypto.SHA1, message, result.data.alioss_access_key_secret, { asBytes: true })
                        this.formData.signature = Crypto.util.bytesToBase64(bytes)
                    }
                } catch (e) {
                    console.error('getAliyunConfig error', e)
                }
            },
			// 选择参考图片
			chooseReferenceImage() {
				uni.chooseImage({
					count: 1, // 只能选择1张图片
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						// 上传图片到服务器
						this.uploadReferenceImages(res.tempFilePaths);
					},
					fail: (err) => {
						console.log('选择参考图片失败:', err);
						this.$sun.toast('选择参考图片失败', 'none');
					}
				});
			},

			// 上传参考图片到服务器
			async uploadReferenceImages(tempFilePaths) {
				uni.showLoading({
					title: '上传中...'
				});

				try {
					const result = await this.uploadSingleReferenceImage(tempFilePaths[0]);
					if (result) {
						// 替换现有图片
						this.referenceImages = result;
						this.imageUrls.push(this.referenceImages)
						this.isReferenceOpen = true
					}
					uni.hideLoading();
					this.$sun.toast('参考图片上传成功', 'success');
				} catch (error) {
					uni.hideLoading();
					console.error('上传参考图片失败:', error);
					this.$sun.toast('参考图片上传失败', 'none');
				}
			},

			// 上传单张参考图片
			uploadSingleReferenceImage(filePath) {
				return new Promise((resolve, reject) => {
					uni.uploadFile({
						url: this.$api.upload,
						filePath: filePath,
						name: 'file',
						formData: {
							uid: uni.getStorageSync('uid')
						},
						success: (res) => {
							try {
								const data = JSON.parse(res.data);
								console.log(data);
								if (data.errno === 0) {
									resolve(data.data);
								} else {
									reject(new Error(data.message || '上传失败'));
								}
							} catch (e) {
								reject(new Error('解析响应失败'));
							}
						},
						fail: (err) => {
							reject(err);
						}
					});
				});
			},

			// 删除参考图片
			deleteReferenceImage(index) {
                this.referenceImages = '';
                // 若只删除图片且视频存在，不清空视频
			},
            // 删除参考视频
            deleteReferenceVideo(){
                this.referenceVideo = ''
            },

			//前往克隆页
			getClone() {
				let compressed = 1;
				if (this.param.isSel == 4) {
					compressed = 2;
				}
				uni.navigateTo({
					url: '/pages/index/clone/clone?compressed=' + compressed
				})
			},
			//确认形象
			confirmImage(obj) {
				this.listObj = obj;

				this.param = {
					decode_img: this.listObj.video_cover,
					id: this.listObj.id,
					isSel: this.param.isSel,
					current_status: this.listObj.current_status,
					new_current_status: this.listObj.new_current_status,
					composite_current_status: this.listObj.composite_current_status,
					four_current_status: this.listObj.four_current_status
				};

				if (this.param.isSel == 3 || this.param.isSel == 2) {
					this.getImage(this.param.decode_img);
				}

				this.isPop = 2;

			},
			async showHistory() {
				const result = await this.$http.post({
					url: this.$api.getAIConversationHistory,
					data: {
						uid: uni.getStorageSync('uid'),
						type: this.type,
					},
				})
				if (result.errno == 0) {
					this.historyList = result.data
					this.isShowHistory = true
					console.log(result.data);
				} else {
					this.$sun.toast(result.message, 'none')
				}
			},
			// 隐藏历史记录
			hideHistory() {
				this.isShowHistory = false
			},
			// 选择历史记录项
			async selectHistoryItem(item) {
				this.session_id = item.dialogue_id
				const result = await this.$http.post({
					url: this.$api.getOneDialogueHistory,
					data: {
						uid: uni.getStorageSync('uid'),
						type: this.type,
						session_id: item.dialogue_id,
					},
				})
				if (result.errno == 0) {
					this.list = result.data
					this.hideHistory()
					this.scrollToBottom()
				} else {
					this.$sun.toast(result.message, 'none')
				}
			},
			// 初始化平台相关信息
			initPlatformInfo() {
				// 获取当前运行的平台
				// #ifdef APP-PLUS
				this.platform = 'app'
				// #endif

				// #ifdef H5
				this.platform = 'h5'
				// #endif

				// #ifdef MP-WEIXIN
				this.platform = 'mp-weixin'
				// #endif

				// 其他平台可以继续添加
				console.log('当前运行平台:', this.platform)
			},
			// 监听键盘高度变化 - 增强版
			listenKeyboardHeight() {
				// 使用uni-app提供的键盘高度变化监听API
				uni.onKeyboardHeightChange((res) => {
					console.log('键盘高度变化:', res.height)

					// 旧的键盘高度
					const oldKeyboardHeight = this.keyboardHeight

					// 设置键盘高度和显示状态
					this.keyboardHeight = res.height
					const wasKeyboardShow = this.isKeyboardShow
					this.isKeyboardShow = res.height > 0

					// 不同平台可能需要不同的处理方式
					if (this.platform === 'app') {
						// App端可能需要特殊处理
						// 某些安卓设备上可能需要额外的偏移量
						if (uni.getSystemInfoSync().platform === 'android') {
							// 安卓平台特殊处理
						}
					}

					// 键盘弹出或高度变化时
					if (this.isKeyboardShow) {
						// 如果是键盘初次弹出或键盘高度变化较大
						if (
							!wasKeyboardShow ||
							Math.abs(oldKeyboardHeight - res.height) > 50
						) {
							// 重新计算布局并滚动到底部
							this.forceLayout()
							this.scrollToBottomDebounced()
						}
					} else if (wasKeyboardShow) {
						// 键盘隐藏时，延迟一下再滚动，避免闪烁
						setTimeout(() => {
							this.forceLayout()
							// 可选：键盘隐藏时也滚动到底部
							// this.scrollToBottom();
						}, 200)
					}
				})
			},
			// 移除键盘高度监听
			removeKeyboardListener() {
				// 使用uni-app提供的方法移除监听
				uni.offKeyboardHeightChange()
			},
			getSafeAreaBottom() {
				// 获取系统信息
				uni.getSystemInfo({
					success: (res) => {
						// 计算底部安全区域
						this.safeAreaBottom = res.safeAreaInsets ?
							res.safeAreaInsets.bottom :
							0
						console.log('安全区域底部高度:', this.safeAreaBottom)
					},
				})
			},
			clearContent() {
				this.content = '';
				this.textareaHeight = this.textareaMinHeight;
				this.inputBarHeight = 120; // 重置为初始高度
			},
			// 获取窗口高度
			getWindowHeight() {
				const systemInfo = uni.getSystemInfoSync()
				this.windowHeight = systemInfo.windowHeight
				console.log('窗口高度:', this.windowHeight)
			},
			// 更新滚动区域高度
			updateContentHeight() {
				const query = uni.createSelectorQuery().in(this)
				query.select('.list').boundingClientRect()
				query.exec((res) => {
					if (res && res[0]) {
						this.contentHeight = res[0].height
						console.log('内容高度:', this.contentHeight)
					}
				})
			},
			// 带防抖功能的滚动到底部，增强键盘交互逻辑
			scrollToBottomDebounced() {
				// 如果键盘状态近期变化或暂时阻止自动滚动，可能需要额外延迟
				const now = Date.now()
				const timeSinceKeyboardChange = now - this.keyboardStateChangeTime

				// 如果键盘状态刚刚变化且变化时间太短，给更长的延迟
				if (timeSinceKeyboardChange < this.keyboardTransitionDuration) {
					console.log('键盘状态刚刚变化，延迟滚动')
					setTimeout(() => {
						// 重新尝试滚动
						this.scrollToBottomDebounced()
					}, this.keyboardTransitionDuration - timeSinceKeyboardChange + 50)
					return
				}

				// 如果暂时阻止自动滚动，则跳过
				if (this.isAutoScrollPrevented) {
					console.log('自动滚动暂时被阻止')
					return
				}

				// 如果已经在滚动中，或者距离上次滚动时间太短，则跳过
				if (
					this.isScrolling ||
					now - this.lastScrollTime < this.scrollDebounceDelay
				) {
					console.log('跳过滚动，防抖中...')
					return
				}

				// 更新滚动状态和时间
				this.isScrolling = true
				this.lastScrollTime = now

				// 执行滚动
				this.scrollToBottom()

				// 一段时间后重置滚动状态
				setTimeout(() => {
					this.isScrolling = false
				}, this.scrollDebounceDelay)
			},
			// 完全重写的滚动到底部方法
			scrollToBottom() {
				// 清除之前的定时器
				clearTimeout(this.scrollTimer)

				// 单步滚动，没有先滚到顶部的步骤
				this.scrollTimer = setTimeout(() => {
					const query = uni.createSelectorQuery().in(this)
					query.select('.list').boundingClientRect()
					query.select('.scroll-area').boundingClientRect()
					query.exec((res) => {
						if (res && res[0] && res[1]) {
							// 列表高度
							const listHeight = res[0].height
							// 滚动区域高度
							const scrollHeight = res[1].height

							// 计算精确的滚动位置 - 列表实际高度减去一些偏移量
							// 这比使用一个大倍数更稳定
							const scrollPosition = Math.max(0, listHeight - scrollHeight + 100)

							// console.log('精确滚动位置:', scrollPosition)

							// 直接设置滚动位置，无需两步式滚动
							this.scrollTop = scrollPosition
						}
					})
				}, 100)
			},
			// 监听滚动事件，可以帮助调试
			onPageScroll(e) {
				// console.log('页面滚动:', e.scrollTop)
			},
			// 输入框获得焦点 - 增强键盘交互
			inputFocus(e) {
				console.log('输入框获得焦点', e)

				// 标记需要滚动到底部
				// this.needScrollToBottom = true
				// 记录键盘状态变化时间
				this.keyboardStateChangeTime = Date.now()

				// H5端可能需要特殊处理
				// #ifdef H5
				// 某些移动浏览器可能不会触发键盘高度变化事件
				clearTimeout(this.focusTimer)
				this.focusTimer = setTimeout(() => {
					if (!this.isKeyboardShow) {
						// 如果键盘还没有被检测到弹出，手动设置键盘状态
						this.isKeyboardShow = true
						// 尝试使用一个估计的键盘高度
						const estimatedKeyboardHeight = this.windowHeight * 0.4 // 大约是屏幕高度的40%
						this.keyboardHeight = estimatedKeyboardHeight
					}

					// 为键盘动画留出足够时间，然后滚动
					setTimeout(() => {
						if (this.isKeyboardShow) {
							// 再次检查，防止状态快速变化
							this.scrollToBottomDebounced()
						}
					}, this.keyboardTransitionDuration)
				}, 300)
				// #endif

				// 其他平台，等待键盘高度变化事件触发后的滚动
			},
			// 输入框失去焦点 - 增强键盘交互
			inputBlur(e) {
				console.log('输入框失去焦点', e)

				// 记录键盘状态变化时间
				this.keyboardStateChangeTime = Date.now()

				// 键盘收起时，短暂阻止自动滚动，避免页面跳动
				this.isAutoScrollPrevented = true
				setTimeout(() => {
					this.isAutoScrollPrevented = false
				}, this.keyboardTransitionDuration + 100)

				// #ifdef H5
				// H5平台特殊处理
				clearTimeout(this.blurTimer)
				this.blurTimer = setTimeout(() => {
					// 如果键盘状态没有被系统事件更新，手动更新
					if (this.isKeyboardShow) {
						this.isKeyboardShow = false
						this.keyboardHeight = 0
					}

					// 强制重新计算布局
					this.forceLayout()
				}, 300)
				// #endif
			},
			// 获取提示词
			async getCueWord() {
				let data = {
					uid: uni.getStorageSync('uid'),
					type: this.type,
				}

				// 如果是type===17且有参考图片，添加图片数据
				if (this.type === 17 || this.type === 18 || this.type === 19 && this.referenceImages) {
					data.img_url = this.referenceImages;
				}

				const result = await this.$http.post({
					url: this.$api.getCueWord,
					data: data,
				})
				if (result.errno == 0) {
					this.cueWord = result.data.content
					console.log(this.cueWord);
					this.session_id = result.data.session_id
					this.list.push({
						role: "assistant",
						content: this.cueWord,
						id: Date.now(),
					})
				} else {
					this.$sun.toast(result.message, 'none')
				}
			},
			// 对话
			startStream() {
				this.list.push({
					role: "assistant",
					content: '',
					id: Date.now(),
				})
				let url = this.$api.getAIConversation;
				if (this.type === 9) {
					url = this.$api.getDeepseek;
				} else if (this.type === 13) {
					url = this.$api.getAIConversationGroup;
				} else if (this.type === 14) {
					url = this.$api.getAIDesigner;
				} else if (this.type === 17 || this.type === 18 || this.type === 19) {
					url = this.$api.imageModify;
				} else if (this.type === 20) {
					url = this.$api.taskStart;
                } else if (this.type === 21) {
                    url = this.$api.getDoubaoSend;
				}
				let data = {
					uid: uni.getStorageSync('uid'), // 请求参数
					content: this.content,
					session_id: this.session_id,
					type: this.type.toString(),
					thinking: this.type ==21 ? this.selectedTagMap && this.selectedTagMap.thinking ? 'enabled' : 'disabled' : undefined,
				}

				// 如果是type===17且有参考图片，添加图片数据
                if ((this.type === 17 || this.type === 18 || this.type === 19|| this.type === 21) && this.referenceImages) {
					data.img_url = this.referenceImages;
					data.reference_images = this.referenceImages;
					console.log('img_url 类型:', typeof data.img_url, '值:', data.img_url);
				} else if (this.type === 20) {
					data.img_url = this.referenceImages;
				}
				if(this.type === 21 && this.referenceVideo){
					data.video_url = this.referenceVideo;
				}


				// 使用流式请求
				this.requestTask = this.$http.requestStream(url, "POST", data, {
					onChunk: (data) => {
						this.content = ''
						// 处理接收到的数据块
						try {
							// 检查数据是否为ArrayBuffer类型
							if (data instanceof ArrayBuffer) {
								// 将ArrayBuffer转换为字符串
								let text = '';
								try {
									// 使用解码函数处理ArrayBuffer
									text = decodedString(data);
									if (!text) {
										throw new Error("解码结果为空");
									}
								} catch (decodeError) {
									console.error("解码ArrayBuffer失败:", decodeError);
									// 尝试使用备用方法
									const buffer = new Uint8Array(data);
									text = String.fromCharCode.apply(null, buffer);
								}
								console.log(text);
								if (this.type === 20) {
									// 尝试解析JSON获取任务ID
									try {
										const jsonData = JSON.parse(text);
										if (jsonData && jsonData.data && jsonData.data.id) {
											console.log('获取到任务ID:', jsonData.data.id);
											// 保存任务ID
											this.taskId = jsonData.data.id;
											// 开始轮询获取结果
											this.pollTaskResult(jsonData.data.id);
										}
									} catch (parseError) {
										console.log('解析任务ID失败:', parseError);
									}
								}
								// 追加到正确的目标属性
								this.list[this.list.length - 1].content += text;
							} else if (typeof data === 'string') {
								// 如果是字符串，直接追加到目标属性
								console.log(data);
								this.list[this.list.length - 1].content += data;
								if (this.type === 20) {
									// 尝试解析JSON获取任务ID
									try {
										const jsonData = JSON.parse(text);
										if (jsonData && jsonData.data && jsonData.data.id) {
											console.log('获取到任务ID:', jsonData.data.id);
											// 保存任务ID
											this.taskId = jsonData.data.id;
											// 开始轮询获取结果
											this.pollTaskResult(jsonData.data.id);
										}
									} catch (parseError) {
										console.log('解析任务ID失败:', parseError);
									}
								}
							} else {
								console.warn("收到未知类型的数据块:", data);
							}

							// 滚动到最新内容
							this.scrollToBottom();
						} catch (error) {
							console.error("处理数据块出错:", error);
						}
					},
					onComplete: () => {
						console.log(`请求完成`);
						let chat = this.list[this.list.length - 1].content
						console.log(chat);
						this.isStart = false



						if (this.type == 13 && this.list[this.list.length - 1].content.includes('@视频创作AI')) {
							this.video_title = ''
							this.video_content = ''
							// 尝试提取并解析JSON
							this.parseVideoJson(chat);
						} else if (this.type === 14) {
							this.parseDesignerJson(chat);
						} else if (this.type === 17 || this.type === 18 || this.type === 19) {
							// 清空参考图片
							this.referenceImages = '';
							try {
								// 解析JSON格式的回复
								const responseData = JSON.parse(chat);
								if (responseData && responseData.errno === 0 && responseData.data) {
									// 设置图片URL
									this.list[this.list.length - 1].img_url = responseData.data;
									// 将图片URL添加到图片URL列表中
									this.imageUrls.push(responseData.data);
									// 清空content，因为这是图片消息
									this.list[this.list.length - 1].content = '';
									console.log('图片URL已设置:', responseData.data);
								}
							} catch (parseError) {
								console.log('解析JSON失败:', parseError);
							}
						} else if (this.type === 21) {
							// 清空参考素材
							this.content = ''
							this.referenceImages = '';
							this.referenceVideo = '';
						}
					},
					onError: (err) => {
						uni.hideLoading();
						this.isStart = false
						console.error(`流式请求错误:`, err);
					}
				});
			},
			// 轮询获取任务结果
			async pollTaskResult(taskId) {
				console.log('开始轮询任务结果，任务ID:', taskId);

				// 清除之前的定时器
				if (this.pollTimer) {
					clearTimeout(this.pollTimer);
				}

				const pollResult = async () => {
					try {
						const result = await this.$http.post({
							url: this.$api.taskEnd,
							data: {
								task_id: taskId,
							}
						});

						console.log('轮询结果:', result);
						console.log(result.data.content);
						if (result.errno === 0) {
							// 任务完成
							if (result.data.content) {
								console.log('任务完成，结果:', result.data.content.video_url);

								// 清空参考图片
								this.referenceImages = '';
								this.content = '';
								// 处理结果数据
								if (result.data.content && result.data.content.video_url) {
									// 设置视频URL
									this.list[this.list.length - 1].video_url = result.data.content.video_url;
									// 将视频URL添加到图片URL列表中（用于预览）
									this.imageUrls.push(result.data.content.video_url);
									// 清空content，因为这是视频消息
									this.list[this.list.length - 1].content = '';
									console.log('视频URL已设置:', result.data.content.video_url);
								}

								// 停止轮询
								this.isStart = false;
								return;
							} else if (result.data && result.data.status === 'failed') {
								// 任务失败
								console.log('任务失败:', result.data);
								this.list[this.list.length - 1].content = '任务执行失败，请重试';
								this.isStart = false;
								return;
							} else {
								// 任务仍在进行中，继续轮询
								console.log('任务进行中，继续轮询...');
								this.list[this.list.length - 1].content = '正在处理中...';
							}
						} else {
							// API调用失败
							console.error('轮询API调用失败:', result.message);
							this.list[this.list.length - 1].content = '获取结果失败: ' + result.message;
							this.isStart = false;
							return;
						}

						// 继续轮询，3秒后再次请求
						this.pollTimer = setTimeout(pollResult, 3000);

					} catch (error) {
						console.error('轮询出错:', error);
						this.list[this.list.length - 1].content = '轮询出错，请重试';
						this.isStart = false;
					}
				};

				// 开始轮询
				pollResult();
			},
			// ai绘画
			async getAiImg(content) {
				this.isStart = true
				this.list.push({
					role: "assistant",
					content: '正在生成中...',
					id: Date.now() + 1, // 添加唯一ID
				})
				const result = await this.$http.post({
					url: this.$api.getGenerationsImg,
					data: {
						content: content,
						uid: uni.getStorageSync('uid'),
						type: this.type,
						session_id: this.session_id,
					},
					loading: false,
				})
				if (result.errno == 0) {
					this.list.pop()
					// 添加生成的图片到列表
					this.list.push({
						role: "assistant",
						img_url: result.data,
						id: Date.now() + 1, // 添加唯一ID
					})

					// 将图片URL添加到图片URL列表中
					this.imageUrls.push(result.data)

					// 图片消息添加后滚动到底部，使用防抖版本
					this.$nextTick(() => {
						this.scrollToBottomDebounced()
					})
				} else {
					this.$sun.toast(result.message, 'none')
					this.list.pop()
					this.list.push({
						role: "assistant",
						content: '生成失败，请重新生成',
						id: Date.now() + 1, // 添加唯一ID
					})
				}
				this.isStart = false
			},
			// 中止请求
			stopStream() {
				if (this.requestTask) {
					this.requestTask.cancel();
					this.requestTask = null;
					console.log('已中止请求');
				}
			},
			// 发送消息
			async send() {
				if (this.isStart) {
					this.$sun.toast('正在生成请稍后', 'none')
					return
				} else {
					this.isStart = true
				}

				this.isReferenceOpen = false

				// 验证发送条件
                // 对于type 17、18、19、20，必须要有图片才能发送；type 21 图片非必传
                if ([17, 18, 19, 20].includes(this.type)) {
					if (!this.referenceImages) {
						this.$sun.toast('请先添加参考图片', 'none')
						this.isStart = false
						return
					}
				} else if(this.type === 21){
					if(!this.referenceImages && !this.referenceVideo && !this.content.trim()){
						this.$sun.toast('请输入内容', 'none')
						this.isStart = false
						return
					}
				} else {
					// 其他类型需要有文本内容
					if (!this.content.trim()) {
						this.$sun.toast('请输入内容', 'none')
						this.isStart = false
						return
					}
				}

				// 设置键盘状态
				const wasKeyboardShow = this.isKeyboardShow

				this.list.push({
					role: "user",
					content: this.content,
					id: Date.now(),
				})

                // 如果是需要传图的类型，或 21 且有图，添加图片到消息中
                if ([17,18,19,20,21].includes(this.type) && this.referenceImages) {
					this.list[this.list.length - 1].reference_images = [this.referenceImages];
				}
                // 如果有参考视频，则把视频预览也放入当前对话消息中（与图片一致的体验）
                if (this.referenceVideo) {
                    this.list[this.list.length - 1].reference_videos = [this.referenceVideo];
                    // 需要时用于统一预览集合（与图片处理保持一致）
                    this.imageUrls.push(this.referenceVideo)
                }

				this.scrollToBottom()

				// 发送消息后，确保足够延迟以适应可能的键盘状态变化
				this.$nextTick(() => {
					// 增加发送后延迟，以适应可能的键盘收起动画
					setTimeout(() => {
						if (!this.isKeyboardShow && wasKeyboardShow) {
							// 如果键盘刚刚收起，给额外时间
							setTimeout(() => {
								this.scrollToBottomDebounced()
							}, this.keyboardTransitionDuration)
						} else {
							this.scrollToBottomDebounced()
						}
					}, 100)
				})

				// if ([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15].includes(this.type)) {
				this.startStream()
				// }
				// else {
				// 	this.getAiImg()
				// }


			},

			imageLoaded(e) {
				console.log('图片加载成功')

				// 图片加载完成后滚动到底部，使用防抖版本
				this.$nextTick(() => {
					this.scrollToBottomDebounced()
				})
			},

			imageError(e) {
				// 图片加载失败的处理
				console.log('图片加载失败')
				this.$sun.toast('图片加载失败', 'none')
			},

			// 滚动到底部事件
			onScrollLower() {
				// console.log('已滚动到底部')
			},
			// 强制重新计算布局
			forceLayout() {
				// 更新内容高度
				this.updateContentHeight()
				// 获取窗口高度
				this.getWindowHeight()
				// 更新横向功能区高度
				this.updateTabRowHeight()
			},
			// 更新横向功能区高度
			updateTabRowHeight() {
				// 检查是否有横向功能区
				if (this.tabBar && this.tabBar.length > 0) {
					const query = uni.createSelectorQuery().in(this);
					query.select('.tab-row').boundingClientRect(rect => {
						if (rect) {
							// 将px转换为rpx
							const pxToRpx = 750 / uni.getSystemInfoSync().windowWidth;
							this.tabRowHeight = Math.max(100, parseInt(rect.height * pxToRpx));
						}
					}).exec();
				}
			},
			// 添加新方法：调整文本域高度
			adjustTextareaHeight(e) {
				// 根据内容自动调整高度，但不超过最大高度
				// auto-height 属性会自动处理高度，这里只需要确保不超过最大高度
				const query = uni.createSelectorQuery().in(this);
				query.select('.r-input').boundingClientRect(rect => {
					if (rect) {
						let height = rect.height + 'px';
						// 将px转换为rpx
						const pxToRpx = 750 / uni.getSystemInfoSync().windowWidth;
						// 提取最大高度数值（去掉'rpx'）
						const maxHeightValue = parseInt(this.textareaMaxHeight);
						const heightInRpx = Math.min(parseInt(rect.height * pxToRpx), maxHeightValue) + 'rpx';
						this.textareaHeight = heightInRpx;

						// 更新输入栏高度，影响底部占位符计算
						// 计算输入框实际高度：文本域高度 + 内边距 + 边框等
						const textareaHeightRpx = parseInt(rect.height * pxToRpx);
						this.inputBarHeight = Math.max(120, textareaHeightRpx + 40); // 40rpx为内边距

						// 如果需要，可以在高度变化后触发滚动到底部
						this.scrollToBottomDebounced();
					}
				}).exec();
			},
			// 解析设计师JSON数据
			parseDesignerJson(text) {
				try {
					// 首先尝试直接将整个文本解析为JSON
					let jsonData = JSON.parse(text);

					// 如果解析成功，检查是否有content字段
					if (jsonData && jsonData.content) {
						console.log('设计师数据解析成功，content值:', jsonData.content);
						// 可以在这里添加提示信息，如：正在生成科技感logo图片！
						// this.$sun.toast('正在生成科技感logo图片！', 'none');
					} else {
						console.log('设计师数据解析成功，但没有content字段:', jsonData);
					}
				} catch (error) {
					// 如果直接解析失败，尝试从文本中提取JSON部分
					try {
						// 尝试用正则表达式匹配JSON格式的内容
						const jsonRegex = /\{[\s\S]*?\}/;
						const match = text.match(jsonRegex);

						if (match && match[0]) {
							// 尝试解析提取出的JSON字符串
							let jsonData = JSON.parse(match[0]);

							if (jsonData && jsonData.content) {
								console.log('从文本中提取并解析设计师数据成功，content值:', jsonData.content);
								// this.$sun.toast('正在生成科技感logo图片！', 'none');
								this.getAiImg(jsonData.content);
							} else {
								console.log('从文本中提取并解析设计师数据成功，但没有content字段:', jsonData);
							}
						} else {
							console.error('未找到有效的JSON数据');
						}
					} catch (extractError) {
						console.error('解析设计师数据失败:', extractError);
					}
				}
			},
			// 添加新方法：解析视频JSON数据
			parseVideoJson(text) {
				try {
					// 尝试从文本中提取JSON部分
					const jsonRegex = /```json\s*([\s\S]*?)\s*```/;
					const match = text.match(jsonRegex);

					if (match && match[1]) {
						// 解析JSON字符串
						const jsonData = JSON.parse(match[1]);

						// 提取title和content并赋值
						if (jsonData.title) {
							this.video_title = jsonData.title;
						}

						if (jsonData.content) {
							this.video_content = jsonData.content;
						}

						uni.navigateTo({
							url: '/pages/index/clip/clip?title=' + this.video_title + '&content=' + this
								.video_content
						})

						console.log('视频数据解析成功:', this.video_title, this.video_content);
					} else {
						console.error('未找到有效的JSON数据');
					}
				} catch (error) {
					console.error('解析JSON数据失败:', error);
				}
			},
		},
	}
</script>

<style lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		position: relative;
		background-color: #080e1e;
		overflow: hidden; // 防止内容溢出
	}

	.history-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 98;
	}

	.history-container {
		position: fixed;
		top: 0;
		left: 0;
		height: 100vh;
		width: 70%;
		background-color: #192236;
		z-index: 99;
		transform: translateX(-100%);
		transition: transform 0.3s ease;
		display: flex;
		flex-direction: column;
		box-shadow: 2rpx 0 10rpx rgba(0, 0, 0, 0.3);
	}

	.history-item-active {
		background-color: #080e1e;
		color: #fff;
	}

	.history-container-active {
		transform: translateX(0);
	}

	.history-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx;
		border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
	}

	.history-title {
		color: #fff;
		font-size: 32rpx;
		font-weight: bold;
	}

	.history-close {
		color: #fff;
		font-size: 40rpx;
	}

	// 参考图片区域样式 - 输入框内嵌版本
	.reference-area-inline {
		display: flex;
		align-items: center;
		gap: 15rpx;
		margin-bottom: 15rpx;
	}

	.reference-btn-inline {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 20rpx;
		width: 70rpx;
		height: 70rpx;
		background-color: #4a4a4a;
		border-radius: 12rpx;
		color: #fff;
		transition: all 0.3s ease;
		border: none;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
		flex-shrink: 0;

		&:active {
			transform: scale(0.95);
			background-color: #5a5a5a;
		}
	}

	.reference-icon {
		font-size: 32rpx;
		font-weight: bold;
		color: #fff;
	}

	.reference-images-inline {
		display: flex;
		align-items: center;
		gap: 12rpx;
	}

	.reference-item-inline {
		position: relative;
		margin-right: 20rpx;
		width: 70rpx;
		height: 70rpx;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
		background-color: #2a2a2a;
		flex-shrink: 0;
		cursor: pointer;
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.95);
		}
	}

	.reference-preview-inline {
		width: 100%;
		height: 100%;
		border-radius: 12rpx;
		object-fit: cover;
	}

	.reference-delete-inline {
		position: absolute;
		top: -6rpx;
		right: -6rpx;
		width: 24rpx;
		height: 24rpx;
		background: rgba(255, 0, 0, 0.8);
		color: #fff;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 18rpx;
		font-weight: bold;
		box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3);
		z-index: 10;

		&:active {
			transform: scale(0.9);
		}
	}

	.reference-overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.6);
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 12rpx;
		opacity: 0;
		transition: opacity 0.3s ease;
	}

	.reference-item-inline:hover .reference-overlay,
	.reference-item-inline:active .reference-overlay {
		opacity: 1;
	}

	.reference-overlay-text {
		color: #fff;
		font-size: 20rpx;
		font-weight: 500;
		text-align: center;
	}

	.history-content {
		flex: 1;
		overflow-y: auto;
		padding: 10rpx 0;
	}

	.history-item {
		padding: 15rpx 20rpx;
		border-bottom: 1rpx solid rgba(255, 255, 255, 0.05);
		cursor: pointer;
	}

	.history-item:active {
		background-color: rgba(255, 255, 255, 0.05);
	}

	.history-item-content {
		color: #fff;
		font-size: 28rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.scroll-area {
		flex: 1;
		height: 100%;
		position: relative;
		/* 确保滚动区域正确显示，防止被输入框覆盖 */
		padding-bottom: env(safe-area-inset-bottom);
	}

	.list {
		padding: 20rpx 30rpx;
		padding-bottom: 40rpx;
		/* 设置内容最小高度，确保短内容时也能正常滚动 */
		min-height: 60vh;
		margin-top: 90rpx;
	}

	/* 确保全局类仍然存在 */
	.in {
		justify-content: flex-end !important;
	}

	.out {
		justify-content: flex-start !important;
	}

	.loading-container {
		display: flex;
		justify-content: center;
		margin: 20rpx 0;

		.loading-dots {
			display: flex;
			align-items: center;

			.dot {
				width: 16rpx;
				height: 16rpx;
				background: rgba(99, 102, 241, 0.6);
				border-radius: 50%;
				margin: 0 6rpx;
				animation: dotPulse 1.5s infinite ease-in-out;

				&:nth-child(2) {
					animation-delay: 0.2s;
				}

				&:nth-child(3) {
					animation-delay: 0.4s;
				}
			}
		}
	}

	.bottom-placeholder {
		transition: height 0.25s ease; // 添加平滑过渡效果
		min-height: 120rpx; // 确保有最小高度
	}

	@keyframes dotPulse {

		0%,
		100% {
			transform: scale(0.6);
			opacity: 0.6;
		}

		50% {
			transform: scale(1);
			opacity: 1;
		}
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: translateY(10rpx);
		}

		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	@keyframes scaleIn {
		from {
			opacity: 0;
			transform: scale(0.9);
		}

		to {
			opacity: 1;
			transform: scale(1);
		}
	}

	/* 生成图片页面底部输入区相关样式 */
	.bottom {
		position: fixed;
		right: 0;
		left: 0;
		display: flex;
		flex-direction: column;
		align-items: stretch;
		justify-content: flex-start;
		padding-bottom: 20rpx;
		background: #000;
		border-top-left-radius: 24rpx;
		border-top-right-radius: 24rpx;
		box-shadow: 0 -8rpx 24rpx rgba(0,0,0,0.08);
		border-top: 0;
		z-index: 10;
		transition: bottom 0.25s ease, transform 0.25s ease;
		will-change: transform, bottom;
	}
	.bottom.keyboard-visible {
		background: #000;
	}

	/* 功能区 */
	.tab-row {
		width: 100%;
		box-sizing: border-box;
		padding: 15rpx;
		margin-bottom: 5rpx;
		border-bottom: 1rpx solid rgba(255,255,255,0.06);
	}
	.tab-scroller {
		display: inline-flex;
		align-items: center;
	}
	.tab-chip {
		display: inline-flex;
		align-items: center;
		padding: 5rpx 15rpx;
		margin-right: 16rpx;
		border-radius: 9999rpx;
		background: rgba(255,255,255,0.06);
		border: 2rpx solid rgba(255,255,255,0.12);
		color: #e5e7eb;
	}
	.tab-chip--active {
		border-color: #60a5fa;
		color: #bfdbfe;
		background: rgba(59,130,246,0.15);
	}
	.tab-label {
		font-size: 28rpx;
	}

	/* 参考图片预览条 */
	.ref-preview-bar {
		width: 100%;
		box-sizing: border-box;
		padding: 10rpx 15px;
		margin-bottom: 5rpx;
		border-bottom: 1rpx solid rgba(255,255,255,0.06);
	}
	.ref-preview-scroller {
		display: inline-flex;
		align-items: center;
	}
	.ref-preview-item {
		position: relative;
		width: 140rpx;
		height: 140rpx;
		border-radius: 16rpx;
		overflow: hidden;
		margin-right: 16rpx;
		background: #fff;
	}
	.ref-preview-img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
  .ref-preview-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 16rpx;
    background: #000;
  }
	.ref-preview-del {
		position: absolute;
		top: 6rpx;
		right: 6rpx;
		width: 36rpx;
		height: 36rpx;
		border-radius: 18rpx;
		background: rgba(0,0,0,.6);
		color: #fff;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 26rpx;
	}
	.ref-preview-add {
		width: 140rpx;
		height: 140rpx;
		border-radius: 16rpx;
		background: #f4f5f7;
		display: flex;
		align-items: center;
		justify-content: center;
		border: 2rpx dashed #cfd6dd;
	}
	.ref-preview-add-icon {
		font-size: 48rpx;
		color: #6b7280;
	}
	.ref-preview-close {
		position: absolute;
		top: 6rpx;
		right: 14rpx;
		width: 40rpx;
		height: 40rpx;
		border-radius: 20rpx;
		background: rgba(0,0,0,.6);
		color: #fff;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 30rpx;
	}

	/* 输入框 */
	.composer {
        margin: 5px 15rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		background: rgba(255,255,255,0.06);
		border-radius: 20rpx;
		padding: 14rpx 18rpx;
		border: 2rpx solid rgba(255,255,255,0.12);
		box-shadow: 0 6rpx 18rpx rgba(2,6,23,0.45);
	}

	.input-container {
		position: relative;
		flex: 1;
		display: flex;
		align-items: center;
	}
	.composer-input {
		margin: 0 12rpx;
	}

	.actions {
		display: flex;
		align-items: center;
		gap: 12rpx;
	}
	.icon {
		font-size: 36rpx;
		color: #e5e7eb;
	}
	.actions .icon-btn {
		background: #3b82f6;
		border-color: #3b82f6;
		color: #ffffff;
		box-shadow: 0 6rpx 16rpx rgba(59,130,246,0.35);
	}
	.actions .icon {
		color: #ffffff;
	}

	.r-input {
		padding: 16rpx;
		background-color: transparent;
		border-radius: 40rpx;
		color: #e5e7eb;
		min-height: 40rpx;
		font-size: 28rpx;
		transition: all 0.3s ease;
		border: 0;
		line-height: 40rpx;
		box-sizing: border-box;
		overflow-y: auto;
	}
	.r-input--light {
		color: #e5e7eb;
	}
	.placeholder-light {
		color: #a5b0bf;
	}

	.clear-btn {
		position: absolute;
		right: 20rpx;
		top: 50%;
		transform: translateY(-50%);
		width: 40rpx;
		height: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 2;
	}

	.placeholder {
		color: rgba(153,153,153,0.7);
		font-size: 28rpx;
	}

/* 更多操作面板 */
		.more-panel {
		padding: 10rpx 15rpx 15rpx 15rpx;
	}
	.more-grid { display: grid; grid-template-columns: repeat(4, 1fr); grid-gap: 20rpx; }
	.more-item { background: rgba(255,255,255,0.06); border: 2rpx solid rgba(255,255,255,0.12); border-radius: 22rpx; padding: 26rpx 0; display:flex; flex-direction:column; align-items:center; justify-content:center; color:#e5e7eb; }
	.more-item-icon { font-size: 40rpx; margin-bottom: 10rpx; }
	.more-item-label { font-size: 28rpx; line-height: 1.5; color: rgb(153,153,153); }
	.send-btn { margin-right: 8rpx; }

	page {
		border-top: none;
		background-color: #080e1e;
		overflow-x: hidden;
	}

	// 预览图片过渡动画
	@keyframes zoomIn {
		from {
			opacity: 0;
			transform: scale(0.8);
		}

		to {
			opacity: 1;
			transform: scale(1);
		}
	}

	@media (max-height: 600px) {

		/* 小屏幕设备额外调整 */
		.bottom-placeholder {
			min-height: 100rpx;
		}

		.r-input {
			height: 36rpx; // 这里仅设置初始高度，不覆盖max-height
			padding: 16rpx 60rpx 16rpx 30rpx;
		}

		.r-but {
			height: 70rpx;
			line-height: 70rpx;
		}
	}
</style>