@font-face {
  font-family: "iconfont"; /* Project id 4995478 */
  src: url('./iconfont.woff2?t=1754987322405') format('woff2'),
       url('./iconfont.woff?t=1754987322405') format('woff'),
       url('./iconfont.ttf?t=1754987322405') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-arrow-left:before {
  content: "\e645";
}

.icon-arrow-right:before {
  content: "\e646";
}

.icon-arrow-up:before {
  content: "\e647";
}

.icon-arrow-down:before {
  content: "\e648";
}

.icon-jiahao:before {
  content: "\e726";
}

.icon-chacha:before {
  content: "\e66a";
}

.icon-lishijilu:before {
  content: "\e642";
}

.icon-xinjianhuihua:before {
  content: "\e933";
}

.icon-shendusikao:before {
  content: "\e7da";
}

.icon-xiangshangyuanjiantoushangjiantoumianxing:before {
  content: "\e8ec";
}

.icon-wenjian:before {
  content: "\e68f";
}

.icon-yuyin:before {
  content: "\e888";
}

.icon-xuanzeshipinwenjian:before {
  content: "\e601";
}

.icon-a-Volume-smallshengyin-xiao:before {
  content: "\e7fd";
}

.icon-a-Video-oneshipin:before {
  content: "\e7fe";
}

.icon-fujian:before {
  content: "\e620";
}

.icon-xiangji1fill:before {
  content: "\e77e";
}

.icon-arrow-1-up:before {
  content: "\e600";
}

.icon-zhaopian_huabanfuben:before {
  content: "\e637";
}

.icon-weibiaoti--:before {
  content: "\e60e";
}

