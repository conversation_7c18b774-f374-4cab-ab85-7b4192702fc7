/**
 * 
 * Interface Enter
 * 
 * 这里放api接口
 * 
 */

const enter = 'https://szr.jiajs.cn/';
// https://aigcys.weijuyunke.cn  aigc.weijuyunke.cn
// const enter = 'https://ai.xwyhealthy.com/';   

const api = {
	//通过合成视频将声音提交到阿里云
	submitSoundConent: 'mobile/clip/submitSoundConent',
	//获取视频字幕
	getSoundConent: 'mobile/clip/getSoundConent',
	//账号采集

	//获取账号作品数量
	HomepageCount: 'mobile/aiCreate/HomepageCount',
	//添加账号采集任务
	saveHomepage: 'mobile/aiCreate/saveHomepage',
	//任务列表
	homepageList: 'mobile/aiCreate/homepageList',
	//主页作品列表
	homevideoList: 'mobile/aiCreate/homevideoList',
	//刷新主页作品数据
	homepageVideoRefresh: 'mobile/aiCreate/homepageVideoRefresh',
	//主页视频文案
	getHomeVideoAudioCopywriting: 'mobile/aiCreate/getHomeVideoAudioCopywriting',
	//视频解析1
	videoAnalysis: 'mobile/aiCreate/createMediaJobs',
	//视频解析2
	homeVideoAudio: 'mobile/aiCreate/describeMediaJob',
	//本地上传视频解析
	localVideo: 'mobile/aiCreate/localVideo',

	//热点追踪
	getHotspot: 'mobile/aiCreate/getHotspot',

	//视频提取
	platformCopywriting: 'mobile/aiCreate/platformCopywriting',

	//矩阵

	//生成快手二维码
	kuaishouQrcode: 'mobile/externalAccount/kuaishouQrcode',
	//抖音地区选择
	liuGuanProvince: 'mobile/index/liuGuanProvince',
	//抖音生成二维码
	douyinQrcode: 'mobile/externalAccount/douyinQrcode',
	//轮询调用检查抖音二维码
	douyinQrcodeCheck: 'mobile/externalAccount/douyinQrcodeCheck',
	//完成抖音授权
	douyinVerifySms: 'mobile/externalAccount/douyinVerifySms',
	//获取视频号二维码
	shipinghaoQrcode: 'mobile/externalAccount/shipinghaoQrcode',
	//轮询调用
	shipinghaoeQrcodeCheck: 'mobile/externalAccount/shipinghaoeQrcodeCheck',
	//获取小红书二维码
	xiaohongshuQrcode: 'mobile/externalAccount/xiaohongshuQrcode',
	//轮询调用
	xiaohongshuUserInfo: 'mobile/externalAccount/xiaohongshuUserInfo',
	//小红书通过来绑定
	xiaohongshuCookie: 'mobile/externalAccount/xiaohongshuCookie',

	//抖音评论列表
	douyinCommentList: 'mobile/externalAccount/douyinCommentList',
	//小红书评论列表
	xiaohongshuCommentList: 'mobile/externalAccount/xiaohongshuCommentList',

	//添加分组
	addAccountGroup: 'mobile/externalAccount/addAccountGroup',
	//分组列表
	accountGroupList: 'mobile/externalAccount/accountGroupList',
	//删除分组
	delAccountGroup: 'mobile/externalAccount/delAccountGroup',
	//账户列表
	accountList: 'mobile/externalAccount/accountList',
	//删除账户
	delAccount: 'mobile/externalAccount/delAccount',
	//切换分组
	accountExchangeGroup: 'mobile/externalAccount/accountExchangeGroup',
	//发布任务
	addVideoTask: 'mobile/externalAccount/addVideoTask',
	//分组账号列表
	accountGroupUser: 'mobile/externalAccount/accountGroupUser',
	//任务列表
	VideoTaskList: 'mobile/externalAccount/VideoTaskList',
	//删除任务接口
	delTask: 'mobile/externalAccount/delTask',
	//终止任务接口
	endTask: 'mobile/externalAccount/endTask',
	//查看发布数据
	accountVideoList: 'mobile/ExternalAccount/VideoList',
	//任务数据统计
	VideoTaskStatistics: 'mobile/ExternalAccount/VideoTaskStatistics',
	//查看POI列表
	poiList: 'mobile/externalAccount/poiList',
	//话题接口
	xiaohongshuTopic: 'mobile/ExternalAccount/xiaohongshuTopic',

	//首页统计
	indexStatistics: 'mobile/externalAccount/statistics',

	//抖音数据刷新
	douyinVideoInfo: 'mobile/timer/douyinVideoInfo',
	//小红书数据刷新
	xiaohongshuVideoInfo: 'mobile/timer/xiaohongshuVideoInfo',
	//快手数据刷新
	kuaishuoVideoInfo: 'mobile/timer/KuaishuoVideoInfo',

	//立即补发
	publishDouyin: 'mobile/externalAccount/publishDouyin',
	releaseXiaohongshu: 'mobile/externalAccount/releaseXiaohongshu',
	publisheKuaishou: 'mobile/externalAccount/publisheKuaishou',


	//添加AI标题记录
	addTitle: 'mobile/aiCreate/addTitle',
	//ai标题记录
	titleList: 'mobile/aiCreate/titleList',
	//删除AI标题
	delTitle: 'mobile/aiCreate/delTitle',
	//查看AI标题
	titleInfo: 'mobile/aiCreate/titleInfo',

	//自定义线路名称
	indexWay: 'mobile/index/way',

	//获取手机号码
	getTelphone: 'mobile/user/getTelphone',
	//短信设置
	noteConfig: 'mobile/index/noteConfig',
	//获取验证码
	sendMobile: 'mobile/index/sendMobile',
	//支付设置
	payconfig: 'mobile/index/payconfig',
	//阿里云配置
	aliyunConfig: 'mobile/index/aliyunConfig',
	//模板设置
	template: 'mobile/index/template',
	//系统设置
	system: 'mobile/index/system',
	//授权
	register: 'mobile/user/register',
	//用户信息
	userInfo: 'mobile/user/userInfo',
	//用户设置
	userSet: 'mobile/user/set',
	//新人领取
	userNewGet: 'mobile/user/newGet',
	//获取openid
	getOpenId: 'mobile/user/getOpenId',
	//导航
	navigation: 'mobile/index/navigation',
	//服务图片
	service: 'mobile/index/service',
	//轮播图
	banner: 'mobile/index/banner',
	//客服设置
	kfSet: 'mobile/index/kfSet',
	//上传图片
	upload: 'mobile/index/upload',
	//微信支付
	pay: 'mobile/index/pay',
	//分销设置
	brokerageSet: 'mobile/brokerage/set',
	//分销首页
	brokerageIndex: 'mobile/brokerage/index',
	//分销统计
	brokerageStatis: 'mobile/brokerage/statis',
	//分销记录
	brokerageLogList: 'mobile/brokerage/logList',
	//提现申请
	brokerageCashwait: 'mobile/brokerage/cashwait',
	//提现记录
	commissionLog: 'mobile/brokerage/commissionLog',
	//我的团队
	brokerageTeamList: 'mobile/brokerage/teamList',
	//会员设置
	memberSet: 'mobile/member/set',
	//会员列表
	memberList: 'mobile/member/memberList',
	//购买会员
	addMemberLog: 'mobile/member/addMemberLog',
	//使用卡密
	useCard: 'mobile/member/useCard',
	//会员开通记录
	memberLogList: 'mobile/member/logList',
	//点数设置
	pointSet: 'mobile/member/pointSet',
	//点数列表
	pointList: 'mobile/member/pointList',
	//点数购买
	addPointLog: 'mobile/member/addPointLog',
	//合伙人设置
	partnerSet: 'mobile/partner/set',
	//申请合伙人
	partnerAdd: 'mobile/partner/apply',
	//合伙人改变状态
	partnerUpdateStatus: 'mobile/partner/updateStatus',
	//合伙人详情
	partnerIndex: 'mobile/partner/info',
	//卡密商城列表
	partnerGoodsList: 'mobile/partner/goodsList',
	//卡密商城详情
	partnerGoodsDetail: 'mobile/partner/goodsDetail',
	//购买卡密
	partnerCardBuy: 'mobile/partner/cardBuy',
	//卡密购买记录
	partnerCardLogList: 'mobile/partner/cardLogList',
	//卡密购买记录删除
	partnerCardLogDel: 'mobile/partner/cardLogDel',
	//卡密列表
	partnerCardList: 'mobile/partner/cardList',
	//扣点设置
	tallySet: 'mobile/index/tallySet',
	//账户明细
	balanceLog: 'mobile/user/balanceLog',

	//人脸图片上传
	faceImageAdd: 'mobile/face/imageAdd',
	//人脸图片列表
	faceImageList: 'mobile/face/imageList',
	//人脸图片删除
	faceImageDel: 'mobile/face/imageDel',
	//换脸预览
	facePreviewAdd: 'mobile/face/previewAdd',
	//换脸预览状态
	facePreviewStatus: 'mobile/face/previewStatus',
	//换脸生成
	faceTaskAdd: 'mobile/face/taskAdd',
	//换脸列表
	faceTaskList: 'mobile/face/taskList',
	//换脸记录删除
	faceTaskDel: 'mobile/face/taskDel',

	//ai创作
	aiCreateAdd: 'mobile/aiCreate/add',
	//创作记录
	aiCreateLog: 'mobile/aiCreate/createList',
	//创作详情
	aiCreateDetail: 'mobile/aiCreate/createDetail',
	//创作删除
	aiCreateDel: 'mobile/aiCreate/createDel',
	//查询是否够点数
	accountInfo: 'mobile/aiCreate/accountInfo',

	//克隆设置
	cloneSet: 'mobile/avatar/cloneSet',
	//公共形象
	characterList: 'mobile/avatar/characterList',
	//公共形象带分页
	characterPage: 'mobile/avatar/characterPage',
	//形象列表
	avatarList: 'mobile/avatar/avatarList',
	//形象快速克隆 //废弃
	avatarAdd: 'mobile/avatar/avatarAdd',
	//形象高级克隆  //废弃
	videoUpload: 'mobile/avatar/videoUpload',
	//形象高级训练  //废弃
	videoTraining: 'mobile/avatar/videoTraining',
	//形象开始训练 线路一
	startTraining: 'mobile/avatar/startTraining',
	//形象深度训练
	continueTranining: 'mobile/avatar/continueTranining',
	//形象删除
	avatarDel: 'mobile/avatar/avatarDel',
	//查询高级单个形象是否克隆成功
	avatarStatus: 'mobile/avatar/avatarStatus',
	//查询标准单个形象是否成功
	compositeAvatarStatus: 'mobile/composite/avatarStatus',

	//高级形象新增 线路二
	towTraining: 'mobile/avatar/towTraining',
	//高级形象新增 双线路推送
	allTraining: 'mobile/avatar/allTraining',
	//视频合成 线路二
	generateTwo: 'mobile/video/generateTwo',
	//上传克隆视频
	uploadAvatar: 'mobile/avatar/uploadAvatar',
	//形象克隆
	avatarClone: 'mobile/avatar/avatarClone',

	//线路四合成
	generateFour: 'mobile/video/generateFour',

	//声音训练
	voiceTraining: 'mobile/voice/training',
	//mega tts 复制接口
	megaTtsCopy: 'mobile/mega_tts/copy',
	//声音训练列表
	voiceTrainList: 'mobile/voice/trainList',
	//自主版音色列表
	megaTtsList: 'mobile/mega_tts/list',
	//自主版音色删除
	megaTtsDel: 'mobile/mega_tts/del',
	//自主版音频合成
	megaTtsSynthesize: 'mobile/mega_tts/synthesize',
	//声音删除
	voiceTrainDel: 'mobile/voice/trainDel',
	//生成音频
	videoSendTts: 'mobile/video/sendTts',
	//音频列表
	videoSoundList: 'mobile/video/soundList',
	//音频删除
	soundDel: 'mobile/video/soundDel',

	//批量合成
	generateVideo: 'mobile/video/generateVideo',

	//科大讯飞

	//文本文案
	trainText: 'mobile/voice/trainText',
	//声音训练
	voiceTaskAdd: 'mobile/voice/voiceTaskAdd',
	//合成音频
	voiceClone: 'mobile/voice/voiceClone',


	//语言列表
	languageList: 'mobile/video/languageList',
	//语音列表
	voiceList: 'mobile/video/voiceList',
	//背景新增
	backgroundAdd: 'mobile/video/backgroundAdd',
	//默认背景
	backgroundDefault: 'mobile/video/backgroundDefault',
	//我的背景
	backgroundList: 'mobile/video/backgroundList',
	//背景删除
	backgroundDel: 'mobile/video/backgroundDel',
	//文字转语音
	// videoSendTts: 'mobile/video/sendTts',
	//唇形选择
	wlList: 'mobile/video/wlList',
	//生成视频
	videoGenerate: 'mobile/video/generate',
	//视频列表
	videoList: 'mobile/video/videoList',
	//视频删除
	videoDel: 'mobile/video/videoDel',

	//下载合成视频
	videoToLocal: 'mobile/video/toLocal',
	//下载换脸视频
	faceToLocal: 'mobile/face/toLocal',

	//高保真录制文本
	getDemonstration: 'mobile/voice/getDemonstration',
	//高保真声音训练
	ttsUpload: 'mobile/voice/ttsUpload',
	//失败后再次提交
	ttsContinue: 'mobile/voice/ttsContinue',
	//高保真声音试听
	ttsVoice: 'mobile/voice/ttsVoice',
	//高保真声音合成(SSML)
	ttsSsml: 'mobile/voice/ttsSsml',

	//上传网络视频
	toLocal: 'mobile/index/toLocal',
	//获取图片宽高
	getImageSize: 'mobile/index/getImageSize',

	//默认唇形
	wlDefault: 'mobile/video/wlDefault',

	//转发视频生成二维码
	videoExhibit: 'mobile/repost/videoExhibit',
	//转发记录
	repostList: 'mobile/repost/repostList',
	//转发统计
	dataStatis: 'mobile/repost/dataStatis',
	//转发数据
	repostData: 'mobile/repost/repostData',
	//图表统计
	workStatis: 'mobile/repost/workStatis',
	//转发设置
	repostSet: 'mobile/repost/set',

	//剪辑
	//转发视频生成二维码
	clipVideoExhibit: 'mobile/repost/clipVideoExhibit',
	//转发记录
	repostClipList: 'mobile/repost/repostClipList',
	//转发统计
	dataClipStatis: 'mobile/repost/dataClipStatis',
	//转发数据
	repostClipData: 'mobile/repost/repostClipData',
	//图表统计
	workClipStatis: 'mobile/repost/workClipStatis',


	//极速形象新增
	speedAvatarAdd: 'mobile/composite/avatarAdd',
	//极速形象删除
	speedAvatarDel: 'mobile/composite/avatarDel',
	//极速形象列表
	speedAvatarList: 'mobile/composite/avatarList',
	//声音新增
	speedSoundAdd: 'mobile/composite/soundAdd',
	//背景新增
	speedBackgroundAdd: 'mobile/composite/backgroundAdd',
	//背景删除
	speedBackgroundDel: 'mobile/composite/backgroundDel',
	//背景列表
	speedBackgroundList: 'mobile/composite/backgroundList',
	//极速视频生成
	speedGenerate: 'mobile/composite/generate',

	//高级形象增加
	synthesisAdd: 'mobile/synthesis/avatarAdd',
	//高级形象列表
	synthesisList: 'mobile/synthesis/avatarList',
	//高级形象生成视频
	synthesisGenerate: 'mobile/synthesis/generate',
	//形象删除
	synthesisDel: 'mobile/synthesis/avatarDel',

	//空列表
	getPage: 'mobile/synthesis/getPage',

	//设置试听
	setDefaultVoice: 'mobile/voice/setDefaultVoice',

	//随机文案
	randExample: 'mobile/voice/randExample',

	//默认音色
	timbreList: 'mobile/video/timbreList',
	//公共音色列表
	voicePackList: 'json/voice_pack.json',
	//音频刷新
	soundRefresh: 'mobile/video/soundRefresh',

	//模板设置
	indexSet: 'mobile/index/indexSet',

	//资产设置
	releaseConfig: 'mobile/resourcesGoods/releaseConfig',
	//共享资产
	releaseGood: 'mobile/resourcesGoods/releaseGood',
	//资产列表
	goodsList: 'mobile/resourcesGoods/goodsList',
	//资产上架下架
	updateStatus: 'mobile/resourcesGoods/updateStatus',
	//编辑资产
	editGoods: 'mobile/resourcesGoods/editGoods',
	//资产删除
	goodsDelete: 'mobile/ResourcesGoods/goodsDelete',
	//购买资产
	createOrder: 'mobile/ResourcesOrder/createOrder',
	//资产订单
	orderList: 'mobile/ResourcesOrder/orderList',
	//资产收益
	resourcesProfitList: 'mobile/Brokerage/resourcesProfitList',
	//帮助教程
	getHelpTutoria: 'mobile/index/getHelpTutoria',
	//时间段
	getTime: 'mobile/resourcesGoods/getTime',

	//客服配置
	customerConfig: 'mobile/index/customerConfig',

	//模板列表
	modelList: 'mobile/Clip/modelList',
	//背景图片列表
	backgroundImgList: 'mobile/Clip/backgroundImgList',
	//图片修改
	imageModify: 'mobile/ai_text/editImg',
	//图生视频-任务发起
	taskStart: 'mobile/ai_text/imgToVideo',
	//图生视频-任务结果
	taskEnd: 'mobile/ai_text/taskList',
	//字体列表
	fontList: 'mobile/Clip/fontList',
	//花字列表
	flowerTextList: 'mobile/Clip/flowerTextList',
	//字幕特效列表
	textSpecial: 'mobile/Clip/textSpecial',
	//添加剪辑任务
	clipTask: 'mobile/Clip/clipTask',
	//音乐分类
	musicCollects: 'mobile/Clip/musicCollects',
	//音乐列表
	musicList: 'mobile/Clip/musicList',
	//剪辑任务列表
	clipTaskList: 'mobile/Clip/clipTaskList',
	//视频列表
	clipVideoList: 'mobile/Clip/clipVideoList',
	//删除剪辑任务列表
	deleteMission: 'mobile/clip/deleteMission',


	//分红统计
	shareholderStatistics: 'mobile/brokerage/shareholderStatistics',
	//分红记录
	shareholderLog: 'mobile/brokerage/shareholderLog',
	//分销等级
	distributionLevel: 'mobile/brokerage/distributionLevel',



	// 添加个人信息
	addUserInfo: 'mobile/user_info/created',
	// 获取个人信息
	getUserInfo: '/mobile/user_info/getOne',
	// 获取账号起号信息/ai_text/account
	getAccount: 'mobile/ai_text/account',
	// 查询起号信息mobile/ai_account/getInfo
	getAccountInfo: 'mobile/ai_account/getInfo',
	// 同城团购/ai_text/account
	getGroupBuy: 'mobile/ai_text/groupBuy',
	// 小红书/ai_text/account
	getReadBook: 'mobile/ai_text/readbook',
	// 朋友圈/ai_text/account
	getFriend: 'mobile/ai_text/friend',
	// 口播文案/ai_text/account
	getOralBroadcasting: 'mobile/ai_text/oralBroadcasting',
	// 海报设计mobile/ai_text/generationsImg
	getGenerationsImg: 'mobile/ai_text/generationsImg',
	// 获取doubaoSend
	getDoubaoSend: 'mobile/ai_dialogue/doubaoSend',
	// 企业定位mobile/ai_text/project
	getAIProject: 'mobile/ai_text/project',
	// 行业诊断报告mobile/ai_text/industryDiagnosis
	getAIProjectIndustryReport: 'mobile/ai_text/industryDiagnosis',
	// 详细运营方案
	getAIProjectOperationPlan: 'mobile/ai_text/operationPlan',
	// AI企业运营计划督导
	getAIProjectOperationSupervision: 'mobile/ai_text/planSupervision',
	// 获取定位结果mobile/ai_company_project/one
	getAIProjectResult: 'mobile/ai_company_project/one',
	// 获取提示词mobile/ai_dialogue/cueWord
	getCueWord: 'mobile/ai_dialogue/cueWord',
	// 收藏内容mobile/user_collect/add
	addFavorite: 'mobile/user_collect/add',
	// AI收藏列表mobile/user_collect/list
	userCollectList: 'mobile/user_collect/list',
	// AI回话mobile/ai_dialogue/start
	getAIConversation: 'mobile/ai_dialogue/start',
	// 获取历史记录mobile/ai_dialogue/getOneDialogueList
	getAIConversationHistory: 'mobile/ai_dialogue/getOneDialogueList',
	// 获取历史记录mobile/ai_dialogue/getOneDialogueHistory
	getOneDialogueHistory: 'mobile/ai_dialogue/getOneDialogueHistory',
	// deepseek提问mobile/ai_dialogue/deepseek
	getDeepseek: 'mobile/ai_dialogue/deepseek',
	// 目标人群mobile/ai_company_project/customer
	getAIProjectCustomer: 'mobile/ai_company_project/customer',
	// 群会话mobile/ai_dialogue/group
	getAIConversationGroup: 'mobile/ai_dialogue/group',
	// AI设计师mobile/ai_dialogue/designer
	getAIDesigner: 'mobile/ai_dialogue/designer',
	// 修改标题mobile/ai_title/update
	updateTitle: 'mobile/ai_title/update',
	// 生成唯一会议IDmobile/meeting_file/generate
	generateMeetingId: 'mobile/meeting_file/generate',
	// 获取会议信息mobile/meeting_file/info
	getMeetingInfo: 'mobile/meeting_file/info',
	// 获取会议列表mobile/meeting_file/list
	getMeetingList: 'mobile/meeting_file/list',
	// 更新会议名称mobile/meeting_file/update
	updateMeetingName: 'mobile/meeting_file/update',
	// 会议录音上传mobile/meeting_file/record
	updateMeetVoice: 'mobile/meeting_file/record',
	// 改写文案mobile/ai_dialogue/copy
	rewriteText: 'mobile/ai_dialogue/copy',
	// 改写文案mobile/ai_dialogue/hotCopy
	rewriteHotText: 'mobile/ai_dialogue/hotCopy',
	// 增加文案记录mobile/ai_text/generateRecord
	addGenerateRecord: 'mobile/ai_text/generateRecord',
	// 获取公共形象mobile/ai_video/avatarList
	getAvatarList: 'mobile/ai_video/avatarList',
	// 使用公共形象mobile/ai_avatar/usage
	usageAvatarList: 'mobile/ai_avatar/usage',

	// 获取分类接口
	getFinishVideoType: 'index/finish_video/type',
	// 获取分类列表接口
	getFinishVideoList: 'index/finish_video/list',
	// 保存立项数据
	saveProject: 'mobile/project/save',
	// 获取立项数据
	getProject: 'mobile/project/get',
	// 改写
	getAiTip: 'mobile/ai_dialogue/tip',
	// 修改AI文案
	seAiCreate: 'mobile/ai_create_v2/update',
	// ai修改mobile/ai_dialogue/spread
	getAiSpread: 'mobile/ai_dialogue/spread',
	// 更新收藏mobile/user_collect/list
	updateCollect: 'mobile/user_collect/update',
	// 取消收藏mobile/user_collect/delete
	deleteCollect: 'mobile/user_collect/delete',
	// 更新账号包装mobile/ai_account/return
	updateAccount: 'mobile/ai_account/update',
	// 视频开关mobile/ai_video/open
	getVideoOpen: 'mobile/ai_video/open',
	// 上传视频添加
	uploadVideoAdd: 'mobile/upload_video/add',
	// 新建目录mobile/upload_video/addType
	uploadVideoAddType: 'mobile/upload_video/addType',
}

for (let key in api) {
	api[key] = enter + api[key];
}

module.exports = api;